package com.xiaohongshu.data.dataark.core.es;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.xiaohongshu.data.dataark.core.common.model.vo.EsSearchResultSourceVO;
import com.xiaohongshu.data.dataark.core.common.model.vo.EsSearchResultVO;
import com.xiaohongshu.dataverse.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
@Slf4j
public class EsService {

    @Value("${es.cluster.config}")
    private String esClusterConfig;

    private static final String MATCH_TEMPLATE = "{\"query\":{\"match\":{\"text\":\"%s\"}},\"sort\":[{\"_score\":\"desc\"},{\"_id\":\"asc\"}],\"size\":%d}";

    private static final String MATCH_PHRASE_TEMPLATE = "{\"query\":{\"match_phrase\":{\"text\":\"%s\"}},\"sort\":[{\"_score\":\"desc\"},{\"_id\":\"asc\"}],\"size\":%d}";

    private static final String MATCH_ALL_TEMPLATE = "{\"query\":{\"match_all\":{}},\"size\":%d}";

    public String indexNameFormat(Long datasetVersionId) {
        String env = System.getProperty("spring.profiles.active");
        if ("local".equalsIgnoreCase(env)) {
            env = "sit";
        }
        return String.format("dataark_%s_dataset_version_%d", env, datasetVersionId);
    }

    public List<EsSearchResultVO> dslQuery(String clusterName, List<String> indexes, String dsl, Long size, List<Object> searchAfter){
        JSONObject jsonObject = JSONObject.parseObject(dsl);
        List<Map<String, String>> sort = new ArrayList<>();
        sort.add( ImmutableMap.of("_score", "desc"));
        sort.add(ImmutableMap.of("_id", "asc"));
        jsonObject.put("sort", sort);
        jsonObject.put("size", size);
        return doSearch(clusterName, indexes, jsonObject.toJSONString(), null);
    }

    public List<EsSearchResultVO> uriQuery(String clusterName, List<String> indexes, String queryParams, Long size, List<Object> searchAfter){
        JSONObject jsonObject = new JSONObject();
        List<Map<String, String>> sort = new ArrayList<>();
        sort.add( ImmutableMap.of("_score", "desc"));
        sort.add(ImmutableMap.of("_id", "asc"));
        jsonObject.put("sort", sort);
        jsonObject.put("size", size);
        return doSearch(clusterName, indexes, jsonObject.toJSONString(), queryParams);
    }

    public List<EsSearchResultVO> matchAll(String clusterName, List<String> indexes, Long size, List<Object> searchAfter) {
        String dsl = String.format(MATCH_ALL_TEMPLATE, size);
        return doSearch(clusterName, indexes, dsl, null);
    }

    public List<EsSearchResultVO> textMatch(String clusterName, List<String> indexes, String keyword, Long size, List<Object> searchAfter) {
        String dsl = String.format(MATCH_TEMPLATE, keyword, size);
        return doSearch(clusterName, indexes, dsl, null);
    }

    public List<EsSearchResultVO> textMatchPhrase(String clusterName, List<String> indexes, String keyword, Long size, List<Object> searchAfter) {
        String dsl = String.format(MATCH_PHRASE_TEMPLATE, keyword, size);
        return doSearch(clusterName, indexes, dsl, null);
    }

    private List<EsSearchResultVO> parseEsSearchResult(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            throw new ServiceException("ES查询结果为空字符串");
        }
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        JSONObject hits = jsonObject.getJSONObject("hits");
        // 暂时用不到？
        Long total = hits.getJSONObject("total").getLong("value");
        JSONArray hitArray = hits.getJSONArray("hits");

        List<EsSearchResultVO> results = new LinkedList<>();
        for (int i = 0; i < hitArray.size(); i++) {
            JSONObject hit = hitArray.getJSONObject(i);

            EsSearchResultSourceVO source = hit.getObject("_source", EsSearchResultSourceVO.class);
            EsSearchResultVO result = EsSearchResultVO.builder()
                    .index(hit.getString("_index"))
                    .docId(hit.getString("_id"))
                    .score(hit.getDouble("_score"))
                    .source(source)
                    .build();
            results.add(result);
        }
        return results;
    }

    public void deleteIndex(String clusterName, Long DatasetVersionId) {
        String indexName = indexNameFormat(DatasetVersionId);

        OkHttpClient client = new OkHttpClient();

        EsClusterConfig clusterConfig = getClusterConfig(clusterName);
        String credentials = clusterConfig.getUsername() + ":" + clusterConfig.getPassword();
        String basicCredential = "Basic " + Base64.getEncoder().encodeToString(credentials.getBytes());

        String url = String.format("http://%s:%d/%s", clusterConfig.getHost(),
                clusterConfig.getPort(), indexName);
        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", basicCredential)
                .delete()
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                log.info("删除索引 {} 成功", indexName);
            } else {
                log.error("delete请求失败");
                responseLog(response);
                throw new ServiceException("删除es索引");
            }
        } catch (IOException e) {
            log.error("删除es索引失败", e);
            throw new ServiceException("查询es失败");
        }
    }

    private void responseLog(Response response){
        String responseBody;
        try {
            responseBody = response.body() != null ? response.body().string() : "";
        } catch (IOException e) {
            log.error("Error reading response body", e);
            throw new RuntimeException(e);
        }
        log.info("Status code: {}, response body: {}", response.code(), responseBody);
    }

    public List<EsSearchResultVO> doSearch(String clusterName, List<String> indexes, String dsl, String queryParams) {
        OkHttpClient client = new OkHttpClient();

        RequestBody body = RequestBody.create(MediaType.parse("application/json"), dsl);

        EsClusterConfig clusterConfig = getClusterConfig(clusterName);

        String credentials = clusterConfig.getUsername() + ":" + clusterConfig.getPassword();
        String basicCredential = "Basic " + Base64.getEncoder().encodeToString(credentials.getBytes());

        String url = String.format("http://%s:%d/%s/_search", clusterConfig.getHost(),
                clusterConfig.getPort(), String.join(",", indexes));
        if (StringUtils.isNotEmpty(queryParams)){
            url += "?q=" + queryParams;
        }

        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", basicCredential)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return parseEsSearchResult(Objects.requireNonNull(response.body()).string());
            } else {
                log.error("查询请求失败");
                responseLog(response);
                throw new ServiceException("查询es失败");
            }
        } catch (Exception e) {
            log.error("es查询请求失败", e);
            throw new ServiceException("查询es失败");
        }
    }

    private EsClusterConfig getClusterConfig(String clusterName) {
        JSONObject configObject = JSONObject.parseObject(esClusterConfig);
        EsClusterConfig object = configObject.getObject(clusterName, EsClusterConfig.class);
        if (ObjectUtils.isEmpty(object)) {
            throw new ServiceException("未找到对应的集群配置");
        }
        return object;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class EsClusterConfig {
        private String username;
        private String password;
        private String host;
        private Integer port;
    }

}
