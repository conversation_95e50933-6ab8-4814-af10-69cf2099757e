package com.xiaohongshu.data.dataark.web;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import red.midware.shaded.com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import static io.vavr.API.printf;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/7
 */
@EnableApolloConfig
@ComponentScan(basePackages = {"com.xiaohongshu.data.dataark", "com.xiaohongshu.infra"})
@SpringBootApplication(exclude = {MybatisPlusAutoConfiguration.class})
public class WebApplication {
    public static void main(String[] args) {
        initEnv();
        SpringApplication.run(WebApplication.class, args);
    }

    private static void initEnv() {
        String env = System.getProperty("spring.profiles.active");
        String appName = System.getProperty("XHS_SERVICE");
        if (StringUtils.isEmpty(appName)) {
            appName = System.getProperty("SERVICE_ID");
        }
        if (StringUtils.isEmpty(appName)) {
            appName = "dataark-service-default";
            System.setProperty("XHS_SERVICE", appName);
        }
        if ("local".equals(env)) {
            env = "sit";
        }
        if ("prod".equals(env)) {
            env = "pro";
        }
        if ("staging".equals(env)) {
            env = "beta";
        }
        System.setProperty("env", env);
        String appid = System.getProperty("APPID");
        if (StringUtils.isEmpty(appid)) {
            System.setProperty("APPID", System.getProperty("XHS_SERVICE"));
        }
    }
}